import cv2
import numpy as np
import time
from typing import Dict, Tuple, Optional
import logging

from .engine_factory import create_inference_engine

logger = logging.getLogger(__name__)


class OrientationClassifier:
    """
    文档图像方向分类器
    
    基于PaddleOCR的PP-LCNet_x1_0_doc_ori模型，使用推理引擎系统进行方向检测
    支持0°, 90°, 180°, 270°四个方向的分类
    """
    
    def __init__(self, args):
        """
        初始化方向分类器
        
        Args:
            args: 配置参数对象，包含以下属性：
                - ori_model_dir: 方向分类模型路径
                - inference_engine: 推理引擎类型 ('onnx', 'openvino')
                - use_gpu: 是否使用GPU

                - ori_input_size: 输入图像尺寸 (默认: [224, 224])
        """
        self.args = args
        
        # 获取配置参数
        self.input_size = getattr(args, 'ori_input_size', [224, 224])
        
        # 类别标签映射 (与PaddleOCR保持一致)
        self.label_map = {
            0: "0",      # 0度
            1: "90",     # 90度  
            2: "180",    # 180度
            3: "270"     # 270度
        }
        
        # 角度映射
        self.angle_map = {
            0: 0,    # 0度
            1: 90,   # 90度
            2: 180,  # 180度
            3: 270   # 270度
        }
        
        # 图像预处理参数 (ImageNet标准)
        self.mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        self.std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        
        # 使用引擎工厂创建推理引擎
        self.inference_engine = create_inference_engine(
            model_path=args.ori_model_dir,
            inference_engine=args.inference_engine,
            use_gpu=args.use_gpu
        )
        
        logger.info(f"方向分类器初始化完成 - 模型: {args.ori_model_dir}")
        logger.info(f"输入尺寸: {self.input_size}")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预处理后的图像张量 (NCHW格式)
        """
        # 转换为RGB
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize到模型输入尺寸
        height, width = self.input_size
        resized = cv2.resize(image, (width, height), interpolation=cv2.INTER_LINEAR)
        
        # 归一化到[0,1]
        normalized = resized.astype(np.float32) / 255.0
        
        # 标准化 (ImageNet标准)
        normalized = (normalized - self.mean) / self.std
        
        # 转换为CHW格式并添加batch维度
        transposed = normalized.transpose(2, 0, 1)  # HWC -> CHW
        batched = np.expand_dims(transposed, axis=0)  # CHW -> NCHW
        
        return batched
    
    def _postprocess_output(self, output: np.ndarray) -> Tuple[int, float, str]:
        """
        后处理模型输出

        Args:
            output: 模型输出概率数组 (已经过softmax处理)
                   例如: [[0.03023089, 0.20277587, 0.03903815, 0.7279551]]

        Returns:
            (class_id, confidence, label_name)
        """
        # 直接从概率数组获取最高置信度的类别
        probabilities = output[0]  # 去除batch维度: [0.03, 0.20, 0.04, 0.73]
        class_id = np.argmax(probabilities)  # 3 (对应270度)
        confidence = probabilities[class_id]  # 0.7279551
        label_name = self.label_map[class_id]  # "270"

        return int(class_id), float(confidence), label_name
    
    def predict(self, image: np.ndarray) -> Dict:
        """
        预测图像方向
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预测结果字典，格式与rotation_detector兼容
        """
        start_time = time.time()
        
        try:
            # 预处理
            preprocessed = self._preprocess_image(image)
            
            # 推理
            inference_start = time.time()
            outputs = self.inference_engine.predict(preprocessed)
            inference_time = time.time() - inference_start
            
            # 后处理
            class_id, confidence, label_name = self._postprocess_output(outputs[0])
            angle = self.angle_map[class_id]
            
            processing_time = time.time() - start_time

            return {
                'success': True,
                'angle': angle,
                'confidence': confidence,
                'class_id': class_id,
                'label_name': label_name,
                'processing_time': processing_time,
                'inference_time': inference_time,
                'error_msg': None,
                'optimization_used': {
                    'level': self.args.inference_engine,
                    'image_resized': True,
                    'roi_detected': False,
                    'preprocessing_applied': True
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"文档方向预测失败: {e}")
            return {
                'success': False,
                'error_msg': f"预测失败: {str(e)}",
                'processing_time': processing_time,
                'angle': 0,
                'confidence': 0.0
            }
    
    def detect_rotation(self, image: np.ndarray) -> Dict:
        """
        检测图像旋转角度 (与RotationDetector接口兼容)
        
        Args:
            image: 输入图像
            
        Returns:
            检测结果字典
        """
        return self.predict(image)
    
    def __call__(self, image: np.ndarray) -> Dict:
        """
        调用接口，与其他预测器保持一致
        
        Args:
            image: 输入图像
            
        Returns:
            预测结果字典
        """
        return self.predict(image)


class OrientationArgs:
    """
    方向分类器参数类
    用于创建OrientationClassifier实例时的参数传递
    """
    
    def __init__(self,
                 ori_model_dir: str = "inference/models/ppstructurev3/doc_img_cls.onnx",
                 inference_engine: str = "onnx",
                 use_gpu: bool = False,

                 ori_input_size: list = None):
        """
        初始化参数
        
        Args:
            ori_model_dir: 方向分类模型路径
            inference_engine: 推理引擎类型
            use_gpu: 是否使用GPU

            ori_input_size: 输入图像尺寸
        """
        self.ori_model_dir = ori_model_dir
        self.inference_engine = inference_engine
        self.use_gpu = use_gpu
        self.ori_input_size = ori_input_size or [224, 224]


def create_orientation_classifier(model_path: str = "inference/models/ppstructurev3/doc_img_cls.onnx",
                                inference_engine: str = "onnx",
                                use_gpu: bool = False) -> OrientationClassifier:
    """
    便捷函数：创建方向分类器
    
    Args:
        model_path: 模型文件路径
        inference_engine: 推理引擎类型
        use_gpu: 是否使用GPU

        
    Returns:
        OrientationClassifier: 方向分类器实例
    """
    args = OrientationArgs(
        ori_model_dir=model_path,
        inference_engine=inference_engine,
        use_gpu=use_gpu
    )
    return OrientationClassifier(args)


# 便捷函数，与现有系统兼容
def detect_doc_orientation(image: np.ndarray,
                          model_path: str = "inference/models/ppstructurev3/doc_img_cls.onnx",
                          inference_engine: str = "onnx",
                          use_gpu: bool = False) -> Dict:
    """
    便捷函数：检测文档图像方向

    Args:
        image: 输入图像
        model_path: 模型路径
        inference_engine: 推理引擎类型
        use_gpu: 是否使用GPU

    Returns:
        检测结果字典
    """
    classifier = create_orientation_classifier(
        model_path=model_path,
        inference_engine=inference_engine,
        use_gpu=use_gpu
    )
    return classifier.detect_rotation(image)
